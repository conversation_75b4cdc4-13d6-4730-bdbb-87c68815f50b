"use client";

import { useState } from "react";
import Hero from "../components/Hero";
import Features from "../components/Features";
import LendAndBorrow from "@/components/LendAndBorrow";
import KredxaServices from "@/components/KredxaServices";
import HowItWorks from "@/components/HowItWorks";
import ReadyToStart from "@/components/ReadyToStart";
import Testimonials from "@/components/Testimonials";
import CustomerReviews from "@/components/CustomerReviews";
import TrustedPartners from "@/components/TrustedPartners";
import Newsletter from "@/components/Newsletter";
import Footer from "@/components/Footer";
import Image from "next/image";
import { Building2, User } from "lucide-react";

export default function Home() {
  const [showModal, setShowModal] = useState(false);

  const handleGetStarted = () => setShowModal(true);

  return (
    <main>
      <Hero />
      <section className="grid grid-cols-1 md:grid-cols-3 gap-8 px-8 md:px-16 py-12">
        <div className="flex justify-center">
          <Image
            src="/interest.svg"
            alt="Yearly Interest"
            width={400}
            height={200}
          />
        </div>
        <div className="flex justify-center">
          <Image
            src="/duration.svg"
            alt="Loan Duration"
            width={400}
            height={200}
          />
        </div>
        <div className="flex justify-center">
          <Image
            src="/free.svg"
            alt="Free as low as 0% "
            width={400}
            height={200}
          />
        </div>
      </section>
      <Features />
      <LendAndBorrow />
      <KredxaServices />
      <HowItWorks />
      <ReadyToStart onGetStarted={handleGetStarted} />
      <Testimonials />
      <CustomerReviews />
      <TrustedPartners />
      <Newsletter />
      <Footer />

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-2xl w-full mx-4">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Choose Account Type</h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              {/* Individual Card */}
              <a
                href="/auth/individual/signup"
                className="border rounded-lg p-6 hover:border-[#1A0505] transition-colors cursor-pointer"
                onClick={() => setShowModal(false)}
              >
                <div className="flex flex-col items-center text-center">
                  <User className="w-16 h-16 mb-4 text-[#1A0505]" />
                  <h3 className="text-xl font-semibold mb-2">For Individual</h3>
                  <p className="text-gray-600">Create an account for personal use</p>
                </div>
              </a>
              {/* Corporate Card */}
              <a
                href="/auth/corporate/signup"
                className="border rounded-lg p-6 hover:border-[#1A0505] transition-colors cursor-pointer"
                onClick={() => setShowModal(false)}
              >
                <div className="flex flex-col items-center text-center">
                  <Building2 className="w-16 h-16 mb-4 text-[#1A0505]" />
                  <h3 className="text-xl font-semibold mb-2">For Corporate</h3>
                  <p className="text-gray-600">Create an account for your business</p>
                </div>
              </a>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}